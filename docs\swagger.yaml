definitions:
  controller.CreateBridgeInput:
    properties:
      is_active:
        description: 使用指针以区分未提供和提供false的情况，默认为true
        type: boolean
      name:
        type: string
      source_channel_type:
        $ref: '#/definitions/types.ChannelType'
      target_channel_id:
        type: integer
      ticket:
        type: string
    required:
    - name
    - source_channel_type
    - target_channel_id
    - ticket
    type: object
  controller.UpdateBridgeInput:
    properties:
      is_active:
        type: boolean
      name:
        type: string
      source_channel_type:
        $ref: '#/definitions/types.ChannelType'
      target_channel_id:
        type: integer
      ticket:
        type: string
    type: object
  models.Bridge:
    properties:
      created_at:
        type: string
      id:
        type: integer
      is_active:
        type: boolean
      name:
        type: string
      source_channel_type:
        $ref: '#/definitions/types.ChannelType'
      target_channel:
        $ref: '#/definitions/models.Channel'
      target_channel_id:
        type: integer
      ticket:
        type: string
      updated_at:
        type: string
    type: object
  models.Channel:
    properties:
      config:
        description: 渠道配置（统一结构）
        items:
          type: integer
        type: array
      created_at:
        type: string
      id:
        description: UUID
        type: integer
      name:
        description: 渠道名称（如：Email-SMTP、OneBot-v11）
        type: string
      type:
        allOf:
        - $ref: '#/definitions/types.ChannelType'
        description: 渠道类型
      updated_at:
        type: string
    type: object
  models.Message:
    properties:
      bridge:
        $ref: '#/definitions/models.Bridge'
      bridge_id:
        type: integer
      content:
        type: string
      created_at:
        type: string
      error_message:
        type: string
      id:
        type: integer
      status:
        $ref: '#/definitions/types.MessageStatus'
      title:
        type: string
      updated_at:
        type: string
    type: object
  types.ChannelType:
    enum:
    - Bark
    - DingTalk
    - FeiShu
    - OneBot
    - Gotify
    - ServerChan
    - PushDeer
    - Synology
    - IYUU
    - PushPlus
    - QMSG
    - WeCom
    - Telegram
    - Email
    - Webhook
    - Ntfy
    - WxPusher
    type: string
    x-enum-comments:
      TypeBark: Bark
      TypeDingTalk: 钉钉机器人
      TypeEmail: 邮箱
      TypeFeiShu: 飞书机器人
      TypeGotify: Gotify
      TypeIYUU: IYUU
      TypeNtfy: Ntfy
      TypeOneBot: OneBot
      TypePushDeer: PushDeer
      TypePushPlus: PushPlus
      TypeQMSG: Qmsg酱
      TypeServerChan: Server酱
      TypeSynology: 群晖chat
      TypeTelegram: Telegram
      TypeWeCom: 企业微信
      TypeWebhook: Webhook
      TypeWxPusher: WxPusher
    x-enum-varnames:
    - TypeBark
    - TypeDingTalk
    - TypeFeiShu
    - TypeOneBot
    - TypeGotify
    - TypeServerChan
    - TypePushDeer
    - TypeSynology
    - TypeIYUU
    - TypePushPlus
    - TypeQMSG
    - TypeWeCom
    - TypeTelegram
    - TypeEmail
    - TypeWebhook
    - TypeNtfy
    - TypeWxPusher
  types.MessageStatus:
    enum:
    - 0
    - 1
    - 2
    - 3
    type: integer
    x-enum-comments:
      StatusFailed: 失败
      StatusPending: 待发送
      StatusSending: 发送中
      StatusSuccess: 已发送
    x-enum-varnames:
    - StatusPending
    - StatusSending
    - StatusSuccess
    - StatusFailed
info:
  contact: {}
paths:
  /api/adapter/list:
    get:
      consumes:
      - application/json
      description: 获取所有适配器列表
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: object
      summary: 获取所有适配器列表
      tags:
      - Adapter
  /api/auth/changePassword:
    post:
      consumes:
      - application/json
      description: 修改密码
      parameters:
      - description: Username
        in: body
        name: username
        required: true
        schema:
          type: string
      - description: Old Password
        in: body
        name: old_password
        required: true
        schema:
          type: string
      - description: New Password
        in: body
        name: new_password
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: object
        "400":
          description: 无效的输入或验证错误
          schema:
            type: object
      summary: 修改密码
      tags:
      - Auth
  /api/auth/login:
    post:
      consumes:
      - application/json
      description: 登录
      parameters:
      - description: Username
        in: body
        name: username
        required: true
        schema:
          type: string
      - description: Password
        in: body
        name: password
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: object
        "400":
          description: 无效的输入或验证错误
          schema:
            type: object
        "401":
          description: 未授权
          schema:
            type: object
      summary: 登录
      tags:
      - Auth
  /api/auth/refresh:
    post:
      consumes:
      - application/json
      description: 刷新令牌
      parameters:
      - description: Token
        in: body
        name: token
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: object
        "400":
          description: 无效的输入或验证错误
          schema:
            type: object
        "401":
          description: 未授权
          schema:
            type: object
      summary: 刷新令牌
      tags:
      - Auth
  /api/auth/register:
    post:
      consumes:
      - application/json
      description: 注册
      parameters:
      - description: Username
        in: body
        name: username
        required: true
        schema:
          type: string
      - description: Password
        in: body
        name: password
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: object
        "400":
          description: 无效的输入或验证错误
          schema:
            type: object
      summary: 注册
      tags:
      - Auth
  /api/bark/{ticket}/push:
    post:
      consumes:
      - application/json
      description: 接收Bark消息
      parameters:
      - description: Ticket
        in: path
        name: ticket
        required: true
        type: string
      - description: Title
        in: formData
        name: title
        type: string
      - description: Subtitle
        in: formData
        name: subtitle
        type: string
      - description: Body
        in: formData
        name: body
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: object
      summary: 接收Bark消息
      tags:
      - Adapter
  /api/bridge/create:
    post:
      consumes:
      - application/json
      description: 创建一个新的消息中转配置。`is_active`默认为true（如果未提供）。
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: Bridge Create Object
        in: body
        name: bridge
        required: true
        schema:
          $ref: '#/definitions/controller.CreateBridgeInput'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.Bridge'
        "400":
          description: 无效的输入或验证错误
          schema:
            type: object
        "500":
          description: 服务器内部错误
          schema:
            type: object
      summary: 创建中转配置
      tags:
      - Bridge
  /api/bridge/delete/{id}:
    delete:
      description: 根据提供的ID删除消息中转配置
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: Bridge ID (UUID)
        format: uuid
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
        "500":
          description: 服务器内部错误
          schema:
            type: object
      summary: 删除中转配置
      tags:
      - Bridge
  /api/bridge/get/{id}:
    get:
      description: 根据提供的ID获取单个消息中转配置的详细信息
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: Bridge ID (UUID)
        format: uuid
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Bridge'
        "404":
          description: 中转配置未找到
          schema:
            type: object
      summary: 根据ID获取中转配置
      tags:
      - Bridge
  /api/bridge/list:
    get:
      description: 获取所有消息中转配置的列表
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Bridge'
            type: array
        "500":
          description: 服务器内部错误
          schema:
            type: object
      summary: 获取所有中转配置
      tags:
      - Bridge
  /api/bridge/update/{id}:
    put:
      consumes:
      - application/json
      description: 根据提供的ID更新现有的消息中转配置。仅更新请求中提供的字段。
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: Bridge ID (UUID)
        format: uuid
        in: path
        name: id
        required: true
        type: string
      - description: Bridge Update Object
        in: body
        name: bridge
        required: true
        schema:
          $ref: '#/definitions/controller.UpdateBridgeInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Bridge'
        "400":
          description: 无效的输入或验证错误
          schema:
            type: object
        "500":
          description: 服务器内部错误
          schema:
            type: object
      summary: 更新中转配置
      tags:
      - Bridge
  /api/channel/create:
    post:
      consumes:
      - application/json
      description: 创建一个新的渠道
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: Channel
        in: body
        name: channel
        required: true
        schema:
          $ref: '#/definitions/models.Channel'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.Channel'
        "400":
          description: 无效的输入或验证错误
          schema:
            type: object
        "500":
          description: 服务器内部错误
          schema:
            type: object
      summary: 创建渠道
      tags:
      - Channel
  /api/channel/delete/{id}:
    delete:
      description: 根据提供的ID删除现有的渠道
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: Channel ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
        "500":
          description: 服务器内部错误
          schema:
            type: object
      summary: 删除渠道
      tags:
      - Channel
  /api/channel/get/{id}:
    get:
      description: 根据提供的ID获取单个渠道的详细信息
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: Channel ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Channel'
        "404":
          description: 渠道未找到
          schema:
            type: object
      summary: 根据ID获取渠道
      tags:
      - Channel
  /api/channel/list:
    get:
      description: 获取所有渠道的列表
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Channel'
            type: array
        "500":
          description: 服务器内部错误
          schema:
            type: object
      summary: 获取所有渠道
      tags:
      - Channel
  /api/channel/test:
    post:
      description: 测试推送
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: Channel
        in: body
        name: channel
        required: true
        schema:
          $ref: '#/definitions/models.Channel'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
        "400":
          description: 无效的输入或验证错误
          schema:
            type: object
        "500":
          description: 服务器内部错误
          schema:
            type: object
      summary: 测试推送
      tags:
      - Channel
  /api/channel/update/{id}:
    put:
      consumes:
      - application/json
      description: 根据提供的ID更新现有的渠道
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: Channel ID
        in: path
        name: id
        required: true
        type: string
      - description: Channel
        in: body
        name: channel
        required: true
        schema:
          $ref: '#/definitions/models.Channel'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Channel'
        "400":
          description: 无效的输入或验证错误
          schema:
            type: object
        "500":
          description: 服务器内部错误
          schema:
            type: object
      summary: 更新渠道
      tags:
      - Channel
  /api/gotify/message:
    post:
      consumes:
      - application/json
      description: 接收Gotify消息
      parameters:
      - description: Token
        in: query
        name: token
        required: true
        type: string
      - description: Title
        in: formData
        name: title
        type: string
      - description: Message
        in: formData
        name: message
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: object
      summary: 接收Gotify消息
      tags:
      - Adapter
  /api/message/create:
    post:
      consumes:
      - application/json
      description: 创建一个新的消息
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: Message
        in: body
        name: message
        required: true
        schema:
          $ref: '#/definitions/models.Message'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.Message'
        "400":
          description: 无效的输入或验证错误
          schema:
            type: object
        "500":
          description: 服务器内部错误
          schema:
            type: object
      summary: 创建消息
      tags:
      - Message
  /api/message/delete/{id}:
    delete:
      description: 根据提供的ID删除消息
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: Message ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
        "404":
          description: 消息未找到
          schema:
            type: object
        "500":
          description: 服务器内部错误
          schema:
            type: object
      summary: 删除消息
      tags:
      - Message
  /api/message/get/{id}:
    get:
      description: 根据提供的ID获取单个消息的详细信息
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: Message ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Message'
        "404":
          description: 消息未找到
          schema:
            type: object
      summary: 根据ID获取消息
      tags:
      - Message
  /api/message/list:
    get:
      description: 获取所有消息的列表
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Message'
            type: array
        "500":
          description: 服务器内部错误
          schema:
            type: object
      summary: 获取所有消息
      tags:
      - Message
  /api/message/update/{id}:
    put:
      consumes:
      - application/json
      description: 根据提供的ID更新现有的消息
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: Message ID
        in: path
        name: id
        required: true
        type: string
      - description: Message
        in: body
        name: message
        required: true
        schema:
          $ref: '#/definitions/models.Message'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Message'
        "400":
          description: 无效的输入或验证错误
          schema:
            type: object
        "500":
          description: 服务器内部错误
          schema:
            type: object
      summary: 更新消息
      tags:
      - Message
  /api/ntfy/{ticket}:
    post:
      consumes:
      - application/json
      description: 接收Ntfy消息
      parameters:
      - description: Ticket
        in: path
        name: ticket
        required: true
        type: string
      - description: Title
        in: formData
        name: title
        type: string
      - description: Message
        in: formData
        name: message
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: object
      summary: 接收Ntfy消息
      tags:
      - Adapter
  /api/onebot/{ticket}/send_msg:
    post:
      consumes:
      - application/json
      description: 接收OneBot消息
      parameters:
      - description: Ticket
        in: path
        name: ticket
        required: true
        type: string
      - description: Message
        in: formData
        name: message
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: object
      summary: 接收OneBot消息
      tags:
      - Adapter
  /api/pushdeer/message/push:
    post:
      consumes:
      - application/json
      description: 接收PushDeer消息
      parameters:
      - description: Token
        in: formData
        name: token
        required: true
        type: string
      - description: Text
        in: formData
        name: text
        type: string
      - description: Desp
        in: formData
        name: desp
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: object
      summary: 接收PushDeer消息
      tags:
      - Adapter
  /api/webhook/{ticket}:
    post:
      consumes:
      - application/json
      description: 接收Webhook消息
      parameters:
      - description: Ticket
        in: path
        name: ticket
        required: true
        type: string
      - description: Title
        in: formData
        name: title
        type: string
      - description: Message
        in: formData
        name: message
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: object
      summary: 接收Webhook消息
      tags:
      - Adapter
swagger: "2.0"

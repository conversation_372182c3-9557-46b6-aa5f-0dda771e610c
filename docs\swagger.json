{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/api/adapter/list": {"get": {"description": "获取所有适配器列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Adapter"], "summary": "获取所有适配器列表", "responses": {"200": {"description": "ok", "schema": {"type": "object"}}}}}, "/api/auth/changePassword": {"post": {"description": "修改密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON>"], "summary": "修改密码", "parameters": [{"description": "Username", "name": "username", "in": "body", "required": true, "schema": {"type": "string"}}, {"description": "Old Password", "name": "old_password", "in": "body", "required": true, "schema": {"type": "string"}}, {"description": "New Password", "name": "new_password", "in": "body", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "ok", "schema": {"type": "object"}}, "400": {"description": "无效的输入或验证错误", "schema": {"type": "object"}}}}}, "/api/auth/login": {"post": {"description": "登录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON>"], "summary": "登录", "parameters": [{"description": "Username", "name": "username", "in": "body", "required": true, "schema": {"type": "string"}}, {"description": "Password", "name": "password", "in": "body", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "ok", "schema": {"type": "object"}}, "400": {"description": "无效的输入或验证错误", "schema": {"type": "object"}}, "401": {"description": "未授权", "schema": {"type": "object"}}}}}, "/api/auth/refresh": {"post": {"description": "刷新令牌", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON>"], "summary": "刷新令牌", "parameters": [{"description": "Token", "name": "token", "in": "body", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "ok", "schema": {"type": "object"}}, "400": {"description": "无效的输入或验证错误", "schema": {"type": "object"}}, "401": {"description": "未授权", "schema": {"type": "object"}}}}}, "/api/auth/register": {"post": {"description": "注册", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON>"], "summary": "注册", "parameters": [{"description": "Username", "name": "username", "in": "body", "required": true, "schema": {"type": "string"}}, {"description": "Password", "name": "password", "in": "body", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "ok", "schema": {"type": "object"}}, "400": {"description": "无效的输入或验证错误", "schema": {"type": "object"}}}}}, "/api/bark/{ticket}/push": {"post": {"description": "接收Bark消息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Adapter"], "summary": "接收Bark消息", "parameters": [{"type": "string", "description": "Ticket", "name": "ticket", "in": "path", "required": true}, {"type": "string", "description": "Title", "name": "title", "in": "formData"}, {"type": "string", "description": "Subtitle", "name": "subtitle", "in": "formData"}, {"type": "string", "description": "Body", "name": "body", "in": "formData"}], "responses": {"200": {"description": "ok", "schema": {"type": "object"}}}}}, "/api/bridge/create": {"post": {"description": "创建一个新的消息中转配置。`is_active`默认为true（如果未提供）。", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Bridge"], "summary": "创建中转配置", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"description": "Bridge Create Object", "name": "bridge", "in": "body", "required": true, "schema": {"$ref": "#/definitions/controller.CreateBridgeInput"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.Bridge"}}, "400": {"description": "无效的输入或验证错误", "schema": {"type": "object"}}, "500": {"description": "服务器内部错误", "schema": {"type": "object"}}}}}, "/api/bridge/delete/{id}": {"delete": {"description": "根据提供的ID删除消息中转配置", "produces": ["application/json"], "tags": ["Bridge"], "summary": "删除中转配置", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "format": "uuid", "description": "Bridge ID (UUID)", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "500": {"description": "服务器内部错误", "schema": {"type": "object"}}}}}, "/api/bridge/get/{id}": {"get": {"description": "根据提供的ID获取单个消息中转配置的详细信息", "produces": ["application/json"], "tags": ["Bridge"], "summary": "根据ID获取中转配置", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "format": "uuid", "description": "Bridge ID (UUID)", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Bridge"}}, "404": {"description": "中转配置未找到", "schema": {"type": "object"}}}}}, "/api/bridge/list": {"get": {"description": "获取所有消息中转配置的列表", "produces": ["application/json"], "tags": ["Bridge"], "summary": "获取所有中转配置", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Bridge"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object"}}}}}, "/api/bridge/update/{id}": {"put": {"description": "根据提供的ID更新现有的消息中转配置。仅更新请求中提供的字段。", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Bridge"], "summary": "更新中转配置", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "format": "uuid", "description": "Bridge ID (UUID)", "name": "id", "in": "path", "required": true}, {"description": "Bridge Update Object", "name": "bridge", "in": "body", "required": true, "schema": {"$ref": "#/definitions/controller.UpdateBridgeInput"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Bridge"}}, "400": {"description": "无效的输入或验证错误", "schema": {"type": "object"}}, "500": {"description": "服务器内部错误", "schema": {"type": "object"}}}}}, "/api/channel/create": {"post": {"description": "创建一个新的渠道", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Channel"], "summary": "创建渠道", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"description": "Channel", "name": "channel", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Channel"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.Channel"}}, "400": {"description": "无效的输入或验证错误", "schema": {"type": "object"}}, "500": {"description": "服务器内部错误", "schema": {"type": "object"}}}}}, "/api/channel/delete/{id}": {"delete": {"description": "根据提供的ID删除现有的渠道", "produces": ["application/json"], "tags": ["Channel"], "summary": "删除渠道", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Channel ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "500": {"description": "服务器内部错误", "schema": {"type": "object"}}}}}, "/api/channel/get/{id}": {"get": {"description": "根据提供的ID获取单个渠道的详细信息", "produces": ["application/json"], "tags": ["Channel"], "summary": "根据ID获取渠道", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Channel ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Channel"}}, "404": {"description": "渠道未找到", "schema": {"type": "object"}}}}}, "/api/channel/list": {"get": {"description": "获取所有渠道的列表", "produces": ["application/json"], "tags": ["Channel"], "summary": "获取所有渠道", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Channel"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object"}}}}}, "/api/channel/test": {"post": {"description": "测试推送", "produces": ["application/json"], "tags": ["Channel"], "summary": "测试推送", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"description": "Channel", "name": "channel", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Channel"}}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "400": {"description": "无效的输入或验证错误", "schema": {"type": "object"}}, "500": {"description": "服务器内部错误", "schema": {"type": "object"}}}}}, "/api/channel/update/{id}": {"put": {"description": "根据提供的ID更新现有的渠道", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Channel"], "summary": "更新渠道", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Channel ID", "name": "id", "in": "path", "required": true}, {"description": "Channel", "name": "channel", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Channel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Channel"}}, "400": {"description": "无效的输入或验证错误", "schema": {"type": "object"}}, "500": {"description": "服务器内部错误", "schema": {"type": "object"}}}}}, "/api/gotify/message": {"post": {"description": "接收Gotify消息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Adapter"], "summary": "接收Gotify消息", "parameters": [{"type": "string", "description": "Token", "name": "token", "in": "query", "required": true}, {"type": "string", "description": "Title", "name": "title", "in": "formData"}, {"type": "string", "description": "Message", "name": "message", "in": "formData"}], "responses": {"200": {"description": "ok", "schema": {"type": "object"}}}}}, "/api/message/create": {"post": {"description": "创建一个新的消息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Message"], "summary": "创建消息", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"description": "Message", "name": "message", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Message"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.Message"}}, "400": {"description": "无效的输入或验证错误", "schema": {"type": "object"}}, "500": {"description": "服务器内部错误", "schema": {"type": "object"}}}}}, "/api/message/delete/{id}": {"delete": {"description": "根据提供的ID删除消息", "produces": ["application/json"], "tags": ["Message"], "summary": "删除消息", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Message ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "404": {"description": "消息未找到", "schema": {"type": "object"}}, "500": {"description": "服务器内部错误", "schema": {"type": "object"}}}}}, "/api/message/get/{id}": {"get": {"description": "根据提供的ID获取单个消息的详细信息", "produces": ["application/json"], "tags": ["Message"], "summary": "根据ID获取消息", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Message ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Message"}}, "404": {"description": "消息未找到", "schema": {"type": "object"}}}}}, "/api/message/list": {"get": {"description": "获取所有消息的列表", "produces": ["application/json"], "tags": ["Message"], "summary": "获取所有消息", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Message"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object"}}}}}, "/api/message/update/{id}": {"put": {"description": "根据提供的ID更新现有的消息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Message"], "summary": "更新消息", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Message ID", "name": "id", "in": "path", "required": true}, {"description": "Message", "name": "message", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Message"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Message"}}, "400": {"description": "无效的输入或验证错误", "schema": {"type": "object"}}, "500": {"description": "服务器内部错误", "schema": {"type": "object"}}}}}, "/api/ntfy/{ticket}": {"post": {"description": "接收Ntfy消息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Adapter"], "summary": "接收Ntfy消息", "parameters": [{"type": "string", "description": "Ticket", "name": "ticket", "in": "path", "required": true}, {"type": "string", "description": "Title", "name": "title", "in": "formData"}, {"type": "string", "description": "Message", "name": "message", "in": "formData"}], "responses": {"200": {"description": "ok", "schema": {"type": "object"}}}}}, "/api/onebot/{ticket}/send_msg": {"post": {"description": "接收OneBot消息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Adapter"], "summary": "接收OneBot消息", "parameters": [{"type": "string", "description": "Ticket", "name": "ticket", "in": "path", "required": true}, {"type": "string", "description": "Message", "name": "message", "in": "formData"}], "responses": {"200": {"description": "ok", "schema": {"type": "object"}}}}}, "/api/pushdeer/message/push": {"post": {"description": "接收PushDeer消息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Adapter"], "summary": "接收PushDeer消息", "parameters": [{"type": "string", "description": "Token", "name": "token", "in": "formData", "required": true}, {"type": "string", "description": "Text", "name": "text", "in": "formData"}, {"type": "string", "description": "<PERSON><PERSON>", "name": "desp", "in": "formData"}], "responses": {"200": {"description": "ok", "schema": {"type": "object"}}}}}, "/api/webhook/{ticket}": {"post": {"description": "接收Webhook消息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Adapter"], "summary": "接收Webhook消息", "parameters": [{"type": "string", "description": "Ticket", "name": "ticket", "in": "path", "required": true}, {"type": "string", "description": "Title", "name": "title", "in": "formData"}, {"type": "string", "description": "Message", "name": "message", "in": "formData"}], "responses": {"200": {"description": "ok", "schema": {"type": "object"}}}}}}, "definitions": {"controller.CreateBridgeInput": {"type": "object", "required": ["name", "source_channel_type", "target_channel_id", "ticket"], "properties": {"is_active": {"description": "使用指针以区分未提供和提供false的情况，默认为true", "type": "boolean"}, "name": {"type": "string"}, "source_channel_type": {"$ref": "#/definitions/types.ChannelType"}, "target_channel_id": {"type": "integer"}, "ticket": {"type": "string"}}}, "controller.UpdateBridgeInput": {"type": "object", "properties": {"is_active": {"type": "boolean"}, "name": {"type": "string"}, "source_channel_type": {"$ref": "#/definitions/types.ChannelType"}, "target_channel_id": {"type": "integer"}, "ticket": {"type": "string"}}}, "models.Bridge": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "integer"}, "is_active": {"type": "boolean"}, "name": {"type": "string"}, "source_channel_type": {"$ref": "#/definitions/types.ChannelType"}, "target_channel": {"$ref": "#/definitions/models.Channel"}, "target_channel_id": {"type": "integer"}, "ticket": {"type": "string"}, "updated_at": {"type": "string"}}}, "models.Channel": {"type": "object", "properties": {"config": {"description": "渠道配置（统一结构）", "type": "array", "items": {"type": "integer"}}, "created_at": {"type": "string"}, "id": {"description": "UUID", "type": "integer"}, "name": {"description": "渠道名称（如：Email-SMTP、OneBot-v11）", "type": "string"}, "type": {"description": "渠道类型", "allOf": [{"$ref": "#/definitions/types.ChannelType"}]}, "updated_at": {"type": "string"}}}, "models.Message": {"type": "object", "properties": {"bridge": {"$ref": "#/definitions/models.Bridge"}, "bridge_id": {"type": "integer"}, "content": {"type": "string"}, "created_at": {"type": "string"}, "error_message": {"type": "string"}, "id": {"type": "integer"}, "status": {"$ref": "#/definitions/types.MessageStatus"}, "title": {"type": "string"}, "updated_at": {"type": "string"}}}, "types.ChannelType": {"type": "string", "enum": ["Bark", "DingTalk", "<PERSON><PERSON><PERSON><PERSON>", "OneBot", "Gotify", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Synology", "IYUU", "PushPlus", "QMSG", "WeCom", "Telegram", "Email", "Webhook", "Ntfy", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "x-enum-comments": {"TypeBark": "Bark", "TypeDingTalk": "钉钉机器人", "TypeEmail": "邮箱", "TypeFeiShu": "飞书机器人", "TypeGotify": "Gotify", "TypeIYUU": "IYUU", "TypeNtfy": "Ntfy", "TypeOneBot": "OneBot", "TypePushDeer": "<PERSON><PERSON><PERSON><PERSON>", "TypePushPlus": "PushPlus", "TypeQMSG": "Qmsg酱", "TypeServerChan": "Server酱", "TypeSynology": "群晖chat", "TypeTelegram": "Telegram", "TypeWeCom": "企业微信", "TypeWebhook": "Webhook", "TypeWxPusher": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "x-enum-varnames": ["TypeBark", "TypeDingTalk", "TypeFeiShu", "TypeOneBot", "TypeGotify", "TypeServerChan", "TypePushDeer", "TypeSynology", "TypeIYUU", "TypePushPlus", "TypeQMSG", "TypeWeCom", "TypeTelegram", "TypeEmail", "TypeWebhook", "TypeNtfy", "TypeWxPusher"]}, "types.MessageStatus": {"type": "integer", "enum": [0, 1, 2, 3], "x-enum-comments": {"StatusFailed": "失败", "StatusPending": "待发送", "StatusSending": "发送中", "StatusSuccess": "已发送"}, "x-enum-varnames": ["StatusPending", "StatusSending", "StatusSuccess", "StatusFailed"]}}}